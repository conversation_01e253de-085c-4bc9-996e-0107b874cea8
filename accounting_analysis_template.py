#!/usr/bin/env python3
"""
Accounting Analysis Template for Medical College Trial Balance
This script categorizes trial balance accounts according to accounting principles
"""

import pandas as pd
import numpy as np
from datetime import datetime

class AccountingAnalyzer:
    def __init__(self):
        # Define account categories based on accounting principles
        self.asset_keywords = [
            'cash', 'bank', 'petty cash', 'inventory', 'stock', 'equipment', 'furniture',
            'building', 'land', 'computer', 'vehicle', 'machinery', 'investment',
            'fixed deposit', 'fd', 'receivable', 'debtor', 'advance', 'prepaid',
            'deposit', 'security deposit', 'sundry debtor', 'trade receivable'
        ]
        
        self.liability_keywords = [
            'payable', 'creditor', 'loan', 'borrowing', 'overdraft', 'outstanding',
            'accrued', 'provision', 'reserve', 'capital', 'retained earning',
            'sundry creditor', 'trade payable', 'salary payable', 'tax payable'
        ]
        
        self.income_keywords = [
            'fee', 'income', 'revenue', 'earning', 'receipt', 'collection',
            'tuition fee', 'admission fee', 'examination fee', 'other income',
            'interest income', 'dividend', 'grant', 'donation'
        ]
        
        self.expense_keywords = [
            'salary', 'wage', 'expense', 'cost', 'payment', 'electricity',
            'water', 'telephone', 'internet', 'rent', 'maintenance', 'repair',
            'stationery', 'printing', 'transport', 'travel', 'medical',
            'insurance', 'depreciation', 'audit fee', 'legal fee'
        ]
    
    def categorize_account(self, account_name, debit_amount, credit_amount):
        """
        Categorize account based on name and balance type
        Returns: (category, subcategory)
        """
        account_lower = account_name.lower()
        
        # Determine if account has debit or credit balance
        has_debit_balance = debit_amount > credit_amount
        has_credit_balance = credit_amount > debit_amount
        
        # Check for specific account types
        for keyword in self.asset_keywords:
            if keyword in account_lower:
                return 'Asset', 'Current Asset' if keyword in ['cash', 'bank', 'inventory', 'debtor'] else 'Fixed Asset'
        
        for keyword in self.liability_keywords:
            if keyword in account_lower:
                if 'capital' in account_lower or 'reserve' in account_lower:
                    return 'Equity', 'Capital & Reserves'
                return 'Liability', 'Current Liability'
        
        for keyword in self.income_keywords:
            if keyword in account_lower:
                return 'Income', 'Operating Income'
        
        for keyword in self.expense_keywords:
            if keyword in account_lower:
                return 'Expense', 'Operating Expense'
        
        # If no keyword match, categorize by balance type (fundamental accounting rule)
        if has_debit_balance:
            # Debit balance = Asset or Expense
            if any(exp in account_lower for exp in ['expense', 'cost', 'payment']):
                return 'Expense', 'Other Expense'
            else:
                return 'Asset', 'Other Asset'
        elif has_credit_balance:
            # Credit balance = Liability, Equity, or Income
            if any(inc in account_lower for inc in ['income', 'fee', 'revenue']):
                return 'Income', 'Other Income'
            else:
                return 'Liability', 'Other Liability'
        
        return 'Unknown', 'Unclassified'
    
    def process_trial_balance(self, trial_balance_data):
        """
        Process trial balance data and categorize accounts
        """
        results = {
            'debtors': [],
            'creditors': [],
            'balance_sheet': {'assets': [], 'liabilities': [], 'equity': []},
            'pnl': {'income': [], 'expenses': []}
        }
        
        for _, row in trial_balance_data.iterrows():
            account_name = row['Account_Name']
            debit_amount = float(row.get('Debit_Amount', 0) or 0)
            credit_amount = float(row.get('Credit_Amount', 0) or 0)
            
            category, subcategory = self.categorize_account(account_name, debit_amount, credit_amount)
            
            account_info = {
                'Account_Name': account_name,
                'Debit_Amount': debit_amount,
                'Credit_Amount': credit_amount,
                'Net_Amount': debit_amount - credit_amount,
                'Category': category,
                'Subcategory': subcategory
            }
            
            # Categorize into appropriate sheets
            if 'debtor' in account_name.lower() or 'receivable' in account_name.lower():
                results['debtors'].append(account_info)
            elif 'creditor' in account_name.lower() or 'payable' in account_name.lower():
                results['creditors'].append(account_info)
            
            if category == 'Asset':
                results['balance_sheet']['assets'].append(account_info)
            elif category == 'Liability':
                results['balance_sheet']['liabilities'].append(account_info)
            elif category == 'Equity':
                results['balance_sheet']['equity'].append(account_info)
            elif category == 'Income':
                results['pnl']['income'].append(account_info)
            elif category == 'Expense':
                results['pnl']['expenses'].append(account_info)
        
        return results
    
    def create_excel_output(self, results, filename='Financial_Analysis_Output.xlsx'):
        """
        Create Excel file with categorized data
        """
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Debtors Sheet
            if results['debtors']:
                debtors_df = pd.DataFrame(results['debtors'])
                debtors_df.to_excel(writer, sheet_name='Debtors', index=False)
            
            # Creditors Sheet
            if results['creditors']:
                creditors_df = pd.DataFrame(results['creditors'])
                creditors_df.to_excel(writer, sheet_name='Creditors', index=False)
            
            # Balance Sheet
            balance_sheet_data = []
            
            # Assets section
            balance_sheet_data.append(['ASSETS', '', '', '', ''])
            for asset in results['balance_sheet']['assets']:
                balance_sheet_data.append([
                    asset['Account_Name'], 
                    asset['Debit_Amount'], 
                    asset['Credit_Amount'],
                    asset['Net_Amount'],
                    asset['Subcategory']
                ])
            
            balance_sheet_data.append(['', '', '', '', ''])
            balance_sheet_data.append(['LIABILITIES & EQUITY', '', '', '', ''])
            
            # Liabilities section
            for liability in results['balance_sheet']['liabilities']:
                balance_sheet_data.append([
                    liability['Account_Name'], 
                    liability['Debit_Amount'], 
                    liability['Credit_Amount'],
                    liability['Net_Amount'],
                    liability['Subcategory']
                ])
            
            # Equity section
            for equity in results['balance_sheet']['equity']:
                balance_sheet_data.append([
                    equity['Account_Name'], 
                    equity['Debit_Amount'], 
                    equity['Credit_Amount'],
                    equity['Net_Amount'],
                    equity['Subcategory']
                ])
            
            balance_sheet_df = pd.DataFrame(balance_sheet_data, 
                                          columns=['Particulars', 'Debit', 'Credit', 'Net Amount', 'Category'])
            balance_sheet_df.to_excel(writer, sheet_name='Balance Sheet', index=False)
            
            # P&L Statement
            pnl_data = []
            pnl_data.append(['INCOME', '', '', '', ''])
            
            for income in results['pnl']['income']:
                pnl_data.append([
                    income['Account_Name'], 
                    income['Debit_Amount'], 
                    income['Credit_Amount'],
                    income['Net_Amount'],
                    income['Subcategory']
                ])
            
            pnl_data.append(['', '', '', '', ''])
            pnl_data.append(['EXPENSES', '', '', '', ''])
            
            for expense in results['pnl']['expenses']:
                pnl_data.append([
                    expense['Account_Name'], 
                    expense['Debit_Amount'], 
                    expense['Credit_Amount'],
                    expense['Net_Amount'],
                    expense['Subcategory']
                ])
            
            pnl_df = pd.DataFrame(pnl_data, 
                                columns=['Particulars', 'Debit', 'Credit', 'Net Amount', 'Category'])
            pnl_df.to_excel(writer, sheet_name='P&L Statement', index=False)
        
        print(f"Excel file created: {filename}")

# Sample usage
if __name__ == "__main__":
    print("Accounting Analysis Template Ready")
    print("Please provide trial balance data in the following format:")
    print("Account_Name, Debit_Amount, Credit_Amount")
    print("\nExample:")
    print("Cash in Hand, 50000, 0")
    print("Bank Loan, 0, 200000")
    print("Tuition Fee Income, 0, 500000")
    print("Salary Expense, 300000, 0")
