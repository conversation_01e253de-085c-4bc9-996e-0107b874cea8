#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to read Trial Balance Excel file and convert to readable format
"""

def read_excel_with_pandas():
    """Try to read Excel file using pandas"""
    try:
        import pandas as pd
        print("Using pandas to read Excel file...")
        
        # Read all sheets
        excel_file = pd.ExcelFile('Trial Balance Medical Clg.xlsx')
        print(f"Available sheets: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n{'='*50}")
            print(f"SHEET: {sheet_name}")
            print(f"{'='*50}")
            
            df = pd.read_excel('Trial Balance Medical Clg.xlsx', sheet_name=sheet_name)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            # Save as CSV
            csv_filename = f"TrialBalance_{sheet_name.replace(' ', '_')}.csv"
            df.to_csv(csv_filename, index=False)
            print(f"Saved as: {csv_filename}")
            
            # Display first 25 rows
            print("\nData preview:")
            print(df.head(25).to_string())
            
        return True
        
    except ImportError:
        print("pandas not available")
        return False
    except Exception as e:
        print(f"Error with pandas: {e}")
        return False

def read_excel_with_openpyxl():
    """Try to read Excel file using openpyxl"""
    try:
        import openpyxl
        print("Using openpyxl to read Excel file...")
        
        workbook = openpyxl.load_workbook('Trial Balance Medical Clg.xlsx')
        print(f"Available sheets: {workbook.sheetnames}")
        
        for sheet_name in workbook.sheetnames:
            print(f"\n{'='*50}")
            print(f"SHEET: {sheet_name}")
            print(f"{'='*50}")
            
            worksheet = workbook[sheet_name]
            
            # Get data
            data = []
            for row in worksheet.iter_rows(values_only=True):
                data.append(row)
            
            print(f"Rows: {len(data)}")
            if data:
                print(f"Columns: {len(data[0]) if data[0] else 0}")
                
                # Save as CSV
                csv_filename = f"TrialBalance_{sheet_name.replace(' ', '_')}.csv"
                with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
                    import csv
                    writer = csv.writer(f)
                    for row in data:
                        writer.writerow(row)
                print(f"Saved as: {csv_filename}")
                
                # Display first 25 rows
                print("\nData preview:")
                for i, row in enumerate(data[:25]):
                    print(f"Row {i+1}: {row}")
        
        return True
        
    except ImportError:
        print("openpyxl not available")
        return False
    except Exception as e:
        print(f"Error with openpyxl: {e}")
        return False

def main():
    print("Attempting to read Trial Balance Excel file...")
    
    # Try pandas first
    if read_excel_with_pandas():
        return
    
    # Try openpyxl
    if read_excel_with_openpyxl():
        return
    
    print("\nCould not read Excel file with available libraries.")
    print("Please convert the Excel file to CSV manually or install required packages:")
    print("pip install pandas openpyxl")

if __name__ == "__main__":
    main()
