# PowerShell script to convert Excel to CSV
try {
    # Create Excel application object
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # Open the Trial Balance Excel file
    $workbook = $excel.Workbooks.Open("$PWD\Trial Balance Medical Clg.xlsx")
    
    # Get all worksheets
    Write-Host "Available worksheets:"
    for ($i = 1; $i -le $workbook.Worksheets.Count; $i++) {
        $worksheet = $workbook.Worksheets.Item($i)
        Write-Host "Sheet $i: $($worksheet.Name)"
        
        # Save each sheet as CSV
        $csvPath = "$PWD\TrialBalance_Sheet$i" + "_$($worksheet.Name).csv"
        $worksheet.SaveAs($csvPath, 6)  # 6 = CSV format
        Write-Host "Saved: $csvPath"
    }
    
    # Close workbook and Excel
    $workbook.Close($false)
    $excel.Quit()
    
    # Release COM objects
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "Conversion completed successfully!"
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Excel might not be installed or file might be corrupted"
}
